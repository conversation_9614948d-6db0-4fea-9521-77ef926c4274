# HTML Restructuring Tools for GPAce Project

This collection of tools will help you batch restructure all HTML files in your GPAce project with beautiful, organized structure, proper commenting, and logical grouping of CSS and JavaScript imports.

## 📁 Files Included

### 🔧 Core Script
- **`restructure-html.ps1`** - Main PowerShell script that performs the restructuring

### 📖 Documentation
- **`HTML-Restructuring-Guide.md`** - Comprehensive documentation with examples and usage instructions
- **`HTML-Restructuring-README.md`** - This file, quick overview of all tools

### 🖱️ Easy-to-Use Batch Files
- **`run-html-restructure.bat`** - Double-click to run the restructuring (with confirmation)
- **`preview-html-restructure.bat`** - Double-click to preview changes without modifying files

## 🚀 Quick Start

### Option 1: Preview First (Recommended)
1. Double-click `preview-html-restructure.bat`
2. Review the changes that would be made
3. If satisfied, double-click `run-html-restructure.bat`

### Option 2: Direct Execution
1. Double-click `run-html-restructure.bat`
2. Confirm when prompted
3. Wait for completion

### Option 3: PowerShell Command Line
```powershell
# Preview changes
.\restructure-html.ps1 -WhatIf

# Apply changes
.\restructure-html.ps1
```

## ✨ What This Does

### Before Restructuring:
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>GPAce</title>
    <link rel="stylesheet" href="css/taskLinks.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/sideDrawer.css" rel="stylesheet">
    <script src="js/workspace-core.js"></script>
</head>
<body>
    <!-- Content -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
```

### After Restructuring:
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- ============================================
         META TAGS & BASIC CONFIGURATION
         ============================================ -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPAce</title>

    <!-- ============================================
         FAVICON & ICONS
         ============================================ -->
    <link rel="icon" type="image/png" href="assets/images/gpace-logo-white.png">
    <link rel="shortcut icon" type="image/png" href="assets/images/gpace-logo-white.png">

    <!-- ============================================
         BOOTSTRAP & UI FRAMEWORKS
         ============================================ -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- ============================================
         COMPONENT STYLES
         ============================================ -->
    <link rel="stylesheet" href="css/taskLinks.css">
    <link href="css/sideDrawer.css" rel="stylesheet">

</head>
<body>
    <!-- Content -->

    <!-- ============================================
         JAVASCRIPT IMPORTS
         ============================================ -->
    <!-- EXTERNAL LIBRARIES -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- CORE APPLICATION -->
    <script src="js/workspace-core.js"></script>

</body>
</html>
```

## 🛡️ Safety Features

- **Automatic Backups**: Original files are backed up to `html-backups/` folder
- **What-If Mode**: Preview changes before applying them
- **Error Handling**: Continues processing even if individual files fail
- **Validation**: Checks file existence and readability before processing

## 📋 Files That Will Be Processed

The script will restructure these HTML files:
- `index.html`
- `grind.html`
- `academic-details.html`
- `workspace.html`
- `extracted.html`
- `daily-calendar.html`
- `guide.html`
- `study-spaces.html`
- `subject-marks.html`
- `settings.html`
- `tasks.html`
- `landing.html`
- `sleep-saboteurs.html`
- `404.html`
- `priority-list.html`
- `priority-calculator.html`
- `flashcards.html`
- `instant-test-feedback.html`
- `relaxed-mode/index.html`

## 🎯 Benefits

### For Developers:
- **Consistent Structure** across all HTML files
- **Easy Navigation** with clear section comments
- **Logical Organization** of CSS and JavaScript imports
- **Better Maintainability** with standardized formatting

### For Performance:
- **Proper Resource Ordering** for optimal loading
- **Preserved Preload Directives** for critical resources
- **Clean Structure** that supports better caching

### For Teams:
- **Standardized Codebase** easier for new developers
- **Clear Conventions** reduce decision fatigue
- **Improved Code Reviews** with consistent formatting

## 🔧 Requirements

- Windows with PowerShell 5.1 or later
- Read/write permissions for HTML files
- About 1-2 minutes for processing all files

## 🆘 Need Help?

1. **Read the full guide**: `HTML-Restructuring-Guide.md`
2. **Start with preview**: Use `preview-html-restructure.bat` first
3. **Check backups**: Original files are in `html-backups/` folder
4. **Restore if needed**: Copy files back from `html-backups/`

## 🎉 Ready to Start?

1. **Preview first**: Double-click `preview-html-restructure.bat`
2. **Apply changes**: Double-click `run-html-restructure.bat`
3. **Enjoy your beautifully structured HTML files!**

---

*Created for the GPAce project to maintain beautiful, organized, and maintainable HTML structure across all files.*
