@echo off
echo ============================================
echo   HTML RESTRUCTURING SCRIPT FOR GPACE
echo ============================================
echo.

echo This script will restructure all HTML files in your project
echo with beautiful, organized structure and proper commenting.
echo.

echo IMPORTANT: This will modify your HTML files!
echo Backups will be created automatically in 'html-backups' folder.
echo.

set /p choice="Do you want to continue? (y/n): "
if /i "%choice%"=="y" goto :run
if /i "%choice%"=="yes" goto :run
goto :cancel

:run
echo.
echo Running HTML restructuring script...
echo.

powershell -ExecutionPolicy Bypass -File "restructure-html.ps1"

echo.
echo Script execution completed!
echo.
echo Check the output above for any errors.
echo If successful, your HTML files have been restructured.
echo Original files are backed up in 'html-backups' folder.
echo.
pause
goto :end

:cancel
echo.
echo Operation cancelled by user.
echo.
pause
goto :end

:end
