# HTML Restructuring Script Documentation

## Overview

The `restructure-html.ps1` PowerShell script is designed to batch restructure all HTML files in the GPAce project with a beautiful, organized, and maintainable structure. It automatically reorganizes CSS imports, JavaScript includes, meta tags, and content sections with proper commenting and indentation.

## Features

### 🎨 **Beautiful Structure Organization**
- **Standardized HTML5 structure** with proper DOCTYPE and semantic organization
- **Organized head section** with clearly commented sections
- **Consistent indentation** and formatting throughout
- **Logical grouping** of related elements

### 📝 **Comprehensive Commenting**
- **Section headers** with ASCII art borders for easy navigation
- **Categorized imports** with descriptive comments
- **Clear separation** between different types of resources

### 🔧 **Smart Content Extraction & Categorization**

#### CSS Organization:
- **Bootstrap & UI Frameworks** - Bootstrap, UI libraries
- **External CDN** - Other CDN-hosted stylesheets
- **Fonts** - Google Fonts and other font resources
- **Local Stylesheets** - Project-specific main stylesheets
- **Component Styles** - Individual component CSS files

#### JavaScript Organization:
- **External Libraries** - CDN-hosted libraries (j<PERSON><PERSON>y, Bootstrap, etc.)
- **Firebase & Auth** - Authentication and Firebase-related scripts
- **Core Application** - Main application logic and managers
- **UI Components** - User interface and interaction scripts
- **Utilities** - Helper functions and utility scripts
- **Inline Scripts** - Custom inline JavaScript code

### 🛡️ **Safety Features**
- **Automatic backups** of original files before modification
- **What-If mode** to preview changes without making them
- **Error handling** with detailed logging
- **File validation** to ensure files exist and are readable

## Usage

### Basic Usage
```powershell
# Run the script with default settings (creates backups)
.\restructure-html.ps1
```

### Advanced Usage Options

#### Preview Mode (Recommended First Run)
```powershell
# See what changes would be made without modifying files
.\restructure-html.ps1 -WhatIf
```

#### Without Backups
```powershell
# Run without creating backup files
.\restructure-html.ps1 -Backup:$false
```

#### Custom Backup Directory
```powershell
# Specify a custom backup directory
.\restructure-html.ps1 -BackupDir "my-custom-backups"
```

#### Combined Options
```powershell
# Preview with custom backup directory
.\restructure-html.ps1 -WhatIf -BackupDir "preview-backups"
```

## Before and After Example

### Before (Unorganized):
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPAce - Current Task</title>
    <link rel="icon" type="image/png" href="assets/images/gpace-logo-white.png">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" as="style">
    <link rel="stylesheet" href="css/taskLinks.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/sideDrawer.css" rel="stylesheet">
    <link href="grind.css" rel="stylesheet">
</head>
<body>
    <!-- Content here -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/workspace-core.js"></script>
    <script src="js/sideDrawer.js"></script>
</body>
</html>
```

### After (Beautifully Organized):
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- ============================================
         META TAGS & BASIC CONFIGURATION
         ============================================ -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPAce - Current Task</title>

    <!-- ============================================
         FAVICON & ICONS
         ============================================ -->
    <link rel="icon" type="image/png" href="assets/images/gpace-logo-white.png">
    <link rel="shortcut icon" type="image/png" href="assets/images/gpace-logo-white.png">

    <!-- ============================================
         PRELOAD CRITICAL RESOURCES
         ============================================ -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" as="style">

    <!-- ============================================
         BOOTSTRAP & UI FRAMEWORKS
         ============================================ -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- ============================================
         LOCAL STYLESHEETS
         ============================================ -->
    <link href="grind.css" rel="stylesheet">

    <!-- ============================================
         COMPONENT STYLES
         ============================================ -->
    <link rel="stylesheet" href="css/taskLinks.css">
    <link href="css/sideDrawer.css" rel="stylesheet">

</head>
<body>
    <!-- Content here -->

    <!-- ============================================
         JAVASCRIPT IMPORTS
         ============================================ -->
    <!-- EXTERNAL LIBRARIES -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- CORE APPLICATION -->
    <script src="js/workspace-core.js"></script>

    <!-- UI COMPONENTS -->
    <script src="js/sideDrawer.js"></script>

</body>
</html>
```

## Files Processed

The script processes the following HTML files:
- `index.html` - Main entry point
- `grind.html` - Grind mode interface
- `academic-details.html` - Academic information page
- `workspace.html` - Workspace interface
- `extracted.html` - Extracted content page
- `daily-calendar.html` - Calendar interface
- `guide.html` - User guide
- `study-spaces.html` - Study spaces management
- `subject-marks.html` - Subject marks tracking
- `settings.html` - Application settings
- `tasks.html` - Task management
- `landing.html` - Landing page
- `sleep-saboteurs.html` - Sleep management
- `404.html` - Error page
- `priority-list.html` - Priority management
- `priority-calculator.html` - Priority calculator
- `flashcards.html` - Flashcard system
- `instant-test-feedback.html` - Test feedback
- `relaxed-mode/index.html` - Relaxed mode interface

## Benefits

### 🚀 **Improved Maintainability**
- Clear organization makes it easy to find and modify specific sections
- Consistent structure across all HTML files
- Helpful comments guide developers to the right sections

### ⚡ **Better Performance**
- Proper ordering of CSS and JavaScript resources
- Maintains preload directives for critical resources
- Organized structure supports better caching strategies

### 🎯 **Enhanced Developer Experience**
- Easy to locate specific types of imports
- Consistent formatting reduces cognitive load
- Clear separation of concerns

### 🔍 **Easier Debugging**
- Organized structure makes it easier to identify issues
- Clear categorization helps isolate problems
- Consistent formatting aids in code review

## Safety and Recovery

### Backup System
- Automatic backups are created by default in the `html-backups` directory
- Original files are preserved with their exact content
- Backups can be disabled with `-Backup:$false` if needed

### Error Handling
- The script validates file existence before processing
- Detailed error messages help identify issues
- Processing continues even if individual files fail

### Recovery
If you need to restore original files:
```powershell
# Copy all backups back to original locations
Copy-Item "html-backups\*" . -Force
```

## Requirements

- **PowerShell 5.1 or later** (Windows PowerShell or PowerShell Core)
- **Read/Write permissions** for HTML files and backup directory
- **UTF-8 encoding support** for proper character handling

## Troubleshooting

### Common Issues

1. **Permission Denied**
   - Ensure you have write permissions to the HTML files
   - Run PowerShell as Administrator if needed

2. **File Not Found**
   - Verify you're running the script from the correct directory
   - Check that all HTML files exist in the expected locations

3. **Encoding Issues**
   - The script uses UTF-8 encoding by default
   - Original file encoding is preserved in backups

### Getting Help

Run the script with `-WhatIf` first to see what changes would be made:
```powershell
.\restructure-html.ps1 -WhatIf
```

This will show you exactly what the script plans to do without making any changes.
