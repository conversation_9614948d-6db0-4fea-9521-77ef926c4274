# Simple HTML Restructuring Test Script
param(
    [string]$FilePath = "index.html",
    [switch]$WhatIf = $false
)

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success { param([string]$Message) Write-ColorOutput $Message "Green" }
function Write-Info { param([string]$Message) Write-ColorOutput $Message "Cyan" }
function Write-Warning { param([string]$Message) Write-ColorOutput $Message "Yellow" }

Write-Info "Testing HTML restructuring on: $FilePath"

if (-not (Test-Path $FilePath)) {
    Write-Warning "File not found: $FilePath"
    exit 1
}

# Read the file
$content = Get-Content $FilePath -Raw -Encoding UTF8

# Extract title
$title = "GPAce - Academic Productivity Platform"
if ($content -match '<title[^>]*>([^<]+)</title>') {
    $title = $matches[1]
}

# Extract CSS links
$cssLinks = @()
$linkMatches = [regex]::Matches($content, '<link[^>]*(?:rel="stylesheet"|href="[^"]*\.css")[^>]*>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
foreach ($match in $linkMatches) {
    if ($match.Value -notmatch 'rel="preload"|rel="icon"|rel="shortcut"') {
        $cssLinks += $match.Value
    }
}

# Extract scripts
$scripts = @()
$scriptMatches = [regex]::Matches($content, '<script[^>]*>[\s\S]*?</script>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
foreach ($match in $scriptMatches) {
    $scripts += $match.Value
}

# Extract body content
$bodyContent = ""
if ($content -match '<body[^>]*>([\s\S]*)</body>') {
    $bodyContent = $matches[1]
    # Remove script tags from body content
    $bodyContent = [regex]::Replace($bodyContent, '<script[^>]*>[\s\S]*?</script>', '', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    $bodyContent = $bodyContent.Trim()
}

# Categorize CSS
$bootstrapCSS = @()
$externalCSS = @()
$localCSS = @()
$componentCSS = @()

foreach ($link in $cssLinks) {
    if ($link -match 'bootstrap') {
        $bootstrapCSS += "    $link"
    } elseif ($link -match 'cdn\.jsdelivr\.net|cdnjs\.cloudflare\.com') {
        $externalCSS += "    $link"
    } elseif ($link -match 'css/') {
        $componentCSS += "    $link"
    } else {
        $localCSS += "    $link"
    }
}

# Categorize Scripts
$externalScripts = @()
$localScripts = @()
$inlineScripts = @()

foreach ($script in $scripts) {
    if ($script -match 'cdn\.jsdelivr\.net|cdnjs\.cloudflare\.com|apis\.google\.com') {
        $externalScripts += "    $script"
    } elseif ($script -match 'src="js/') {
        $localScripts += "    $script"
    } else {
        $inlineScripts += "    $script"
    }
}

# Build new HTML
$newHTML = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- ============================================
         META TAGS & BASIC CONFIGURATION
         ============================================ -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$title</title>

    <!-- ============================================
         FAVICON & ICONS
         ============================================ -->
    <link rel="icon" type="image/png" href="assets/images/gpace-logo-white.png">
    <link rel="shortcut icon" type="image/png" href="assets/images/gpace-logo-white.png">

"@

if ($bootstrapCSS.Count -gt 0) {
    $newHTML += @"
    <!-- ============================================
         BOOTSTRAP & UI FRAMEWORKS
         ============================================ -->
$(($bootstrapCSS) -join "`n")

"@
}

if ($externalCSS.Count -gt 0) {
    $newHTML += @"
    <!-- ============================================
         EXTERNAL CDN STYLESHEETS
         ============================================ -->
$(($externalCSS) -join "`n")

"@
}

if ($localCSS.Count -gt 0) {
    $newHTML += @"
    <!-- ============================================
         LOCAL STYLESHEETS
         ============================================ -->
$(($localCSS) -join "`n")

"@
}

if ($componentCSS.Count -gt 0) {
    $newHTML += @"
    <!-- ============================================
         COMPONENT STYLES
         ============================================ -->
$(($componentCSS) -join "`n")

"@
}

$newHTML += @"
</head>
<body>
$bodyContent

    <!-- ============================================
         JAVASCRIPT IMPORTS
         ============================================ -->
"@

if ($externalScripts.Count -gt 0) {
    $newHTML += @"
    <!-- EXTERNAL LIBRARIES -->
$(($externalScripts) -join "`n")

"@
}

if ($localScripts.Count -gt 0) {
    $newHTML += @"
    <!-- LOCAL SCRIPTS -->
$(($localScripts) -join "`n")

"@
}

if ($inlineScripts.Count -gt 0) {
    $newHTML += @"
    <!-- INLINE SCRIPTS -->
$(($inlineScripts) -join "`n")

"@
}

$newHTML += @"
</body>
</html>
"@

if ($WhatIf) {
    Write-Info "WHAT-IF MODE: Would restructure $FilePath"
    Write-Info "Title: $title"
    Write-Info "CSS Links found: $($cssLinks.Count)"
    Write-Info "Scripts found: $($scripts.Count)"
    Write-Info "Bootstrap CSS: $($bootstrapCSS.Count)"
    Write-Info "External CSS: $($externalCSS.Count)"
    Write-Info "Local CSS: $($localCSS.Count)"
    Write-Info "Component CSS: $($componentCSS.Count)"
    Write-Info "External Scripts: $($externalScripts.Count)"
    Write-Info "Local Scripts: $($localScripts.Count)"
    Write-Info "Inline Scripts: $($inlineScripts.Count)"
} else {
    # Create backup
    $backupPath = "$FilePath.backup"
    Copy-Item $FilePath $backupPath -Force
    Write-Info "Backup created: $backupPath"
    
    # Write new file
    $newHTML | Out-File -FilePath $FilePath -Encoding UTF8 -NoNewline
    Write-Success "Successfully restructured $FilePath"
}

Write-Info "Test completed!"
