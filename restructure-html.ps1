# HTML Restructuring <PERSON>ript for GPAce Project
# This script batch restructures all HTML files with beautiful, organized structure

param(
    [switch]$WhatIf = $false,
    [switch]$Backup = $true,
    [string]$BackupDir = "html-backups"
)

# Color output functions
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success { param([string]$Message) Write-ColorOutput $Message "Green" }
function Write-Warning { param([string]$Message) Write-ColorOutput $Message "Yellow" }
function Write-Error { param([string]$Message) Write-ColorOutput $Message "Red" }
function Write-Info { param([string]$Message) Write-ColorOutput $Message "Cyan" }

# HTML files to process
$htmlFiles = @(
    'index.html',
    'grind.html',
    'academic-details.html',
    'workspace.html',
    'extracted.html',
    'daily-calendar.html',
    'guide.html',
    'study-spaces.html',
    'subject-marks.html',
    'settings.html',
    'tasks.html',
    'landing.html',
    'sleep-saboteurs.html',
    '404.html',
    'priority-list.html',
    'priority-calculator.html',
    'flashcards.html',
    'instant-test-feedback.html',
    'relaxed-mode/index.html'
)

# Create backup directory if needed
if ($Backup -and -not $WhatIf) {
    if (-not (Test-Path $BackupDir)) {
        New-Item -ItemType Directory -Path $BackupDir | Out-Null
        Write-Info "Created backup directory: $BackupDir"
    }
}

function Get-PageTitle {
    param([string]$Content)
    if ($Content -match '<title[^>]*>([^<]+)</title>') {
        return $matches[1]
    }
    return "GPAce - Academic Productivity Platform"
}

function Extract-MetaTags {
    param([string]$Content)
    $metaTags = @()
    
    # Extract charset
    if ($Content -match '<meta\s+charset="([^"]+)"[^>]*>') {
        $metaTags += '    <meta charset="' + $matches[1] + '">'
    } else {
        $metaTags += '    <meta charset="UTF-8">'
    }
    
    # Extract viewport
    if ($Content -match '<meta\s+name="viewport"[^>]*content="([^"]+)"[^>]*>') {
        $metaTags += '    <meta name="viewport" content="' + $matches[1] + '">'
    } else {
        $metaTags += '    <meta name="viewport" content="width=device-width, initial-scale=1.0">'
    }
    
    # Extract other meta tags
    $otherMetas = [regex]::Matches($Content, '<meta\s+(?!charset|name="viewport")[^>]*>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    foreach ($meta in $otherMetas) {
        if ($meta.Value -notmatch 'charset|viewport') {
            $metaTags += '    ' + $meta.Value
        }
    }
    
    return $metaTags
}

function Extract-PreloadLinks {
    param([string]$Content)
    $preloads = @()
    $preloadMatches = [regex]::Matches($Content, '<link[^>]*rel="preload"[^>]*>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    foreach ($match in $preloadMatches) {
        $preloads += '    ' + $match.Value
    }
    return $preloads
}

function Extract-CSSLinks {
    param([string]$Content)
    $cssLinks = @()
    
    # Extract all CSS links (excluding preloads and favicons)
    $linkMatches = [regex]::Matches($Content, '<link[^>]*(?:rel="stylesheet"|href="[^"]*\.css")[^>]*>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    foreach ($match in $linkMatches) {
        if ($match.Value -notmatch 'rel="preload"|rel="icon"|rel="shortcut"') {
            $cssLinks += '    ' + $match.Value
        }
    }
    
    return $cssLinks
}

function Extract-Scripts {
    param([string]$Content)
    $scripts = @()
    
    # Extract all script tags
    $scriptMatches = [regex]::Matches($Content, '<script[^>]*>[\s\S]*?</script>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    foreach ($match in $scriptMatches) {
        $scripts += '    ' + $match.Value
    }
    
    return $scripts
}

function Extract-BodyContent {
    param([string]$Content)
    
    # Extract content between <body> and </body>, excluding scripts
    if ($Content -match '<body[^>]*>([\s\S]*)</body>') {
        $bodyContent = $matches[1]
        
        # Remove script tags from body content
        $bodyContent = [regex]::Replace($bodyContent, '<script[^>]*>[\s\S]*?</script>', '', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        
        # Clean up extra whitespace
        $bodyContent = [regex]::Replace($bodyContent, '\n\s*\n\s*\n', "`n`n", [System.Text.RegularExpressions.RegexOptions]::Multiline)
        
        return $bodyContent.Trim()
    }
    
    return ""
}

function Categorize-CSSLinks {
    param([array]$CSSLinks)
    
    $categorized = @{
        'External CDN' = @()
        'Bootstrap & UI Frameworks' = @()
        'Fonts' = @()
        'Local Stylesheets' = @()
        'Component Styles' = @()
    }
    
    foreach ($link in $CSSLinks) {
        if ($link -match 'cdn\.jsdelivr\.net|cdnjs\.cloudflare\.com|unpkg\.com') {
            if ($link -match 'bootstrap') {
                $categorized['Bootstrap & UI Frameworks'] += $link
            } else {
                $categorized['External CDN'] += $link
            }
        } elseif ($link -match 'fonts\.googleapis\.com|fonts\.gstatic\.com') {
            $categorized['Fonts'] += $link
        } elseif ($link -match 'css/') {
            $categorized['Component Styles'] += $link
        } else {
            $categorized['Local Stylesheets'] += $link
        }
    }
    
    return $categorized
}

function Categorize-Scripts {
    param([array]$Scripts)

    $categorized = @{
        'External Libraries' = @()
        'Firebase & Auth' = @()
        'Core Application' = @()
        'UI Components' = @()
        'Utilities' = @()
        'Inline Scripts' = @()
    }

    foreach ($script in $Scripts) {
        if ($script -match 'cdn\.jsdelivr\.net|cdnjs\.cloudflare\.com|apis\.google\.com|accounts\.google\.com') {
            $categorized['External Libraries'] += $script
        } elseif ($script -match 'firebase|auth') {
            $categorized['Firebase & Auth'] += $script
        } elseif ($script -match 'src="js/.*(?:core|main|manager|integration)') {
            $categorized['Core Application'] += $script
        } elseif ($script -match 'src="js/.*(?:ui|drawer|modal|display)') {
            $categorized['UI Components'] += $script
        } elseif ($script -match 'src="js/') {
            $categorized['Utilities'] += $script
        } else {
            $categorized['Inline Scripts'] += $script
        }
    }

    return $categorized
}

function Build-RestructuredHTML {
    param(
        [string]$Title,
        [array]$MetaTags,
        [array]$PreloadLinks,
        [hashtable]$CategorizedCSS,
        [string]$BodyContent,
        [hashtable]$CategorizedScripts
    )

    $html = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- ============================================
         META TAGS & BASIC CONFIGURATION
         ============================================ -->
$(($MetaTags | ForEach-Object { $_ }) -join "`n")
    <title>$Title</title>

    <!-- ============================================
         FAVICON & ICONS
         ============================================ -->
    <link rel="icon" type="image/png" href="assets/images/gpace-logo-white.png">
    <link rel="shortcut icon" type="image/png" href="assets/images/gpace-logo-white.png">

"@

    # Add preload links if they exist
    if ($PreloadLinks.Count -gt 0) {
        $html += @"
    <!-- ============================================
         PRELOAD CRITICAL RESOURCES
         ============================================ -->
$(($PreloadLinks | ForEach-Object { $_ }) -join "`n")

"@
    }

    # Add CSS sections
    foreach ($category in @('Bootstrap & UI Frameworks', 'External CDN', 'Fonts', 'Local Stylesheets', 'Component Styles')) {
        if ($CategorizedCSS[$category].Count -gt 0) {
            $sectionTitle = $category.ToUpper().Replace(' ', ' ')
            $html += @"
    <!-- ============================================
         $sectionTitle
         ============================================ -->
$(($CategorizedCSS[$category] | ForEach-Object { $_ }) -join "`n")

"@
        }
    }

    $html += @"
</head>
<body>
$BodyContent

    <!-- ============================================
         JAVASCRIPT IMPORTS
         ============================================ -->
"@

    # Add script sections
    foreach ($category in @('External Libraries', 'Firebase & Auth', 'Core Application', 'UI Components', 'Utilities', 'Inline Scripts')) {
        if ($CategorizedScripts[$category].Count -gt 0) {
            $sectionTitle = $category.ToUpper().Replace(' ', ' ')
            $html += @"
    <!-- $sectionTitle -->
$(($CategorizedScripts[$category] | ForEach-Object { $_ }) -join "`n")

"@
        }
    }

    $html += @"
</body>
</html>
"@

    return $html
}

function Format-HTML {
    param([string]$HTML)

    # Basic HTML formatting
    $lines = $HTML -split "`n"
    $formatted = @()
    $indentLevel = 0
    $inScript = $false
    $inStyle = $false

    foreach ($line in $lines) {
        $trimmedLine = $line.Trim()

        if ($trimmedLine -eq '') {
            $formatted += ''
            continue
        }

        # Handle script and style tags (preserve their internal formatting)
        if ($trimmedLine -match '<script[^>]*>') { $inScript = $true }
        if ($trimmedLine -match '<style[^>]*>') { $inStyle = $true }
        if ($trimmedLine -match '</script>') { $inScript = $false }
        if ($trimmedLine -match '</style>') { $inStyle = $false }

        # Decrease indent for closing tags
        if ($trimmedLine -match '^</(?!br|hr|img|input|meta|link)' -and -not $inScript -and -not $inStyle) {
            $indentLevel = [Math]::Max(0, $indentLevel - 1)
        }

        # Add the line with proper indentation
        if ($inScript -or $inStyle) {
            $formatted += $line  # Preserve original formatting in scripts/styles
        } else {
            $indent = '    ' * $indentLevel
            $formatted += $indent + $trimmedLine
        }

        # Increase indent for opening tags
        if ($trimmedLine -match '^<(?!br|hr|img|input|meta|link|/).*[^/]>$' -and -not $inScript -and -not $inStyle) {
            $indentLevel++
        }
    }

    return ($formatted -join "`n")
}

function Process-HTMLFile {
    param([string]$FilePath)

    Write-Info "Processing: $FilePath"

    if (-not (Test-Path $FilePath)) {
        Write-Warning "File not found: $FilePath"
        return $false
    }

    try {
        # Read the original file
        $originalContent = Get-Content $FilePath -Raw -Encoding UTF8

        if ([string]::IsNullOrWhiteSpace($originalContent)) {
            Write-Warning "File is empty: $FilePath"
            return $false
        }

        # Create backup if requested
        if ($Backup -and -not $WhatIf) {
            $backupPath = Join-Path $BackupDir (Split-Path $FilePath -Leaf)
            Copy-Item $FilePath $backupPath -Force
            Write-Info "  Backup created: $backupPath"
        }

        # Extract components
        $title = Get-PageTitle $originalContent
        $metaTags = Extract-MetaTags $originalContent
        $preloadLinks = Extract-PreloadLinks $originalContent
        $cssLinks = Extract-CSSLinks $originalContent
        $scripts = Extract-Scripts $originalContent
        $bodyContent = Extract-BodyContent $originalContent

        # Categorize components
        $categorizedCSS = Categorize-CSSLinks $cssLinks
        $categorizedScripts = Categorize-Scripts $scripts

        # Build restructured HTML
        $restructuredHTML = Build-RestructuredHTML -Title $title -MetaTags $metaTags -PreloadLinks $preloadLinks -CategorizedCSS $categorizedCSS -BodyContent $bodyContent -CategorizedScripts $categorizedScripts

        # Format the HTML
        $formattedHTML = Format-HTML $restructuredHTML

        if ($WhatIf) {
            Write-Info "  [WHAT-IF] Would restructure $FilePath"
            Write-Info "  [WHAT-IF] Title: $title"
            Write-Info "  [WHAT-IF] CSS Links: $($cssLinks.Count)"
            Write-Info "  [WHAT-IF] Scripts: $($scripts.Count)"
        } else {
            # Write the restructured file
            $formattedHTML | Out-File -FilePath $FilePath -Encoding UTF8 -NoNewline
            Write-Success "  ✓ Successfully restructured $FilePath"
        }

        return $true

    } catch {
        Write-Error "  ✗ Error processing $FilePath`: $($_.Exception.Message)"
        return $false
    }
}

# Main execution
Write-ColorOutput "============================================" "Magenta"
Write-ColorOutput "  HTML RESTRUCTURING SCRIPT FOR GPACE" "Magenta"
Write-ColorOutput "============================================" "Magenta"
Write-Info ""

if ($WhatIf) {
    Write-Warning "WHAT-IF MODE: No files will be modified"
    Write-Info ""
}

$successCount = 0
$totalCount = 0

foreach ($file in $htmlFiles) {
    $totalCount++

    if (Process-HTMLFile $file) {
        $successCount++
    }

    Write-Info ""
}

# Summary
Write-ColorOutput "============================================" "Magenta"
Write-ColorOutput "  RESTRUCTURING COMPLETE" "Magenta"
Write-ColorOutput "============================================" "Magenta"

if ($WhatIf) {
    Write-Info "WHAT-IF MODE: $totalCount files would be processed"
} else {
    Write-Success "Successfully processed: $successCount/$totalCount files"

    if ($Backup) {
        Write-Info "Backups saved to: $BackupDir"
    }
}

Write-Info ""
Write-Info "Script completed at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

# Additional features and usage examples
Write-ColorOutput "============================================" "Cyan"
Write-ColorOutput "  USAGE EXAMPLES" "Cyan"
Write-ColorOutput "============================================" "Cyan"
Write-Info "# Run in preview mode (no changes made):"
Write-Info ".\restructure-html.ps1 -WhatIf"
Write-Info ""
Write-Info "# Run without creating backups:"
Write-Info ".\restructure-html.ps1 -Backup:`$false"
Write-Info ""
Write-Info "# Run with custom backup directory:"
Write-Info ".\restructure-html.ps1 -BackupDir 'my-backups'"
Write-Info ""
Write-ColorOutput "============================================" "Cyan"
