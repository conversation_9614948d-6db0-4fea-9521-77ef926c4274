# HTML Restructuring <PERSON>ript for GPAce Project
# This script batch restructures all HTML files with beautiful, organized structure

param(
    [switch]$WhatIf = $false,
    [switch]$Backup = $true,
    [string]$BackupDir = "html-backups"
)

# Color output functions
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success { param([string]$Message) Write-ColorOutput $Message "Green" }
function Write-Warning { param([string]$Message) Write-ColorOutput $Message "Yellow" }
function Write-Error { param([string]$Message) Write-ColorOutput $Message "Red" }
function Write-Info { param([string]$Message) Write-ColorOutput $Message "Cyan" }

# HTML files to process
$htmlFiles = @(
    'index.html',
    'grind.html',
    'academic-details.html',
    'workspace.html',
    'extracted.html',
    'daily-calendar.html',
    'study-spaces.html',
    'subject-marks.html',
    'settings.html',
    'tasks.html',
    'landing.html',
    'sleep-saboteurs.html',
    '404.html',
    'priority-list.html',
    'priority-calculator.html',
    'flashcards.html',
    'instant-test-feedback.html'
)

# Create backup directory if needed
if ($Backup -and -not $WhatIf) {
    if (-not (Test-Path $BackupDir)) {
        New-Item -ItemType Directory -Path $BackupDir | Out-Null
        Write-Info "Created backup directory: $BackupDir"
    }
}

function Get-PageTitle {
    param([string]$Content)
    if ($Content -match '<title[^>]*>([^<]+)</title>') {
        return $matches[1]
    }
    return "GPAce - Academic Productivity Platform"
}

function Extract-CSSLinks {
    param([string]$Content)
    $cssLinks = @()
    
    # Extract all CSS links (excluding preloads and favicons)
    $linkMatches = [regex]::Matches($Content, '<link[^>]*(?:rel="stylesheet"|href="[^"]*\.css")[^>]*>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    foreach ($match in $linkMatches) {
        if ($match.Value -notmatch 'rel="preload"|rel="icon"|rel="shortcut"') {
            $cssLinks += $match.Value
        }
    }
    
    return $cssLinks
}

function Extract-Scripts {
    param([string]$Content)
    $scripts = @()
    
    # Extract all script tags
    $scriptMatches = [regex]::Matches($Content, '<script[^>]*>[\s\S]*?</script>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    foreach ($match in $scriptMatches) {
        $scripts += $match.Value
    }
    
    return $scripts
}

function Extract-BodyContent {
    param([string]$Content)
    
    # Extract content between <body> and </body>, excluding scripts
    if ($Content -match '<body[^>]*>([\s\S]*)</body>') {
        $bodyContent = $matches[1]
        
        # Remove script tags from body content
        $bodyContent = [regex]::Replace($bodyContent, '<script[^>]*>[\s\S]*?</script>', '', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
        
        # Clean up extra whitespace
        $bodyContent = [regex]::Replace($bodyContent, '\n\s*\n\s*\n', "`n`n", [System.Text.RegularExpressions.RegexOptions]::Multiline)
        
        return $bodyContent.Trim()
    }
    
    return ""
}

function Categorize-CSSLinks {
    param([array]$CSSLinks)
    
    $bootstrap = @()
    $external = @()
    $fonts = @()
    $local = @()
    $components = @()
    
    foreach ($link in $CSSLinks) {
        if ($link -match 'bootstrap') {
            $bootstrap += "    $link"
        } elseif ($link -match 'cdn\.jsdelivr\.net|cdnjs\.cloudflare\.com|unpkg\.com') {
            $external += "    $link"
        } elseif ($link -match 'fonts\.googleapis\.com|fonts\.gstatic\.com') {
            $fonts += "    $link"
        } elseif ($link -match 'css/') {
            $components += "    $link"
        } else {
            $local += "    $link"
        }
    }
    
    return @{
        'Bootstrap' = $bootstrap
        'External' = $external
        'Fonts' = $fonts
        'Local' = $local
        'Components' = $components
    }
}

function Categorize-Scripts {
    param([array]$Scripts)
    
    $external = @()
    $firebase = @()
    $core = @()
    $ui = @()
    $utilities = @()
    $inline = @()
    
    foreach ($script in $Scripts) {
        if ($script -match 'cdn\.jsdelivr\.net|cdnjs\.cloudflare\.com|apis\.google\.com|accounts\.google\.com') {
            $external += "    $script"
        } elseif ($script -match 'firebase|auth') {
            $firebase += "    $script"
        } elseif ($script -match 'src="js/.*(?:core|main|manager|integration)') {
            $core += "    $script"
        } elseif ($script -match 'src="js/.*(?:ui|drawer|modal|display)') {
            $ui += "    $script"
        } elseif ($script -match 'src="js/') {
            $utilities += "    $script"
        } else {
            $inline += "    $script"
        }
    }
    
    return @{
        'External' = $external
        'Firebase' = $firebase
        'Core' = $core
        'UI' = $ui
        'Utilities' = $utilities
        'Inline' = $inline
    }
}

function Build-RestructuredHTML {
    param(
        [string]$Title,
        [hashtable]$CategorizedCSS,
        [string]$BodyContent,
        [hashtable]$CategorizedScripts
    )
    
    $html = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- ============================================
         META TAGS & BASIC CONFIGURATION
         ============================================ -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>$Title</title>

    <!-- ============================================
         FAVICON & ICONS
         ============================================ -->
    <link rel="icon" type="image/png" href="assets/images/gpace-logo-white.png">
    <link rel="shortcut icon" type="image/png" href="assets/images/gpace-logo-white.png">

"@

    # Add CSS sections
    if ($CategorizedCSS['Bootstrap'].Count -gt 0) {
        $html += @"
    <!-- ============================================
         BOOTSTRAP & UI FRAMEWORKS
         ============================================ -->
$(($CategorizedCSS['Bootstrap']) -join "`n")

"@
    }

    if ($CategorizedCSS['External'].Count -gt 0) {
        $html += @"
    <!-- ============================================
         EXTERNAL CDN STYLESHEETS
         ============================================ -->
$(($CategorizedCSS['External']) -join "`n")

"@
    }

    if ($CategorizedCSS['Fonts'].Count -gt 0) {
        $html += @"
    <!-- ============================================
         FONTS
         ============================================ -->
$(($CategorizedCSS['Fonts']) -join "`n")

"@
    }

    if ($CategorizedCSS['Local'].Count -gt 0) {
        $html += @"
    <!-- ============================================
         LOCAL STYLESHEETS
         ============================================ -->
$(($CategorizedCSS['Local']) -join "`n")

"@
    }

    if ($CategorizedCSS['Components'].Count -gt 0) {
        $html += @"
    <!-- ============================================
         COMPONENT STYLES
         ============================================ -->
$(($CategorizedCSS['Components']) -join "`n")

"@
    }

    $html += @"
</head>
<body>
$BodyContent

    <!-- ============================================
         JAVASCRIPT IMPORTS
         ============================================ -->
"@

    # Add script sections
    if ($CategorizedScripts['External'].Count -gt 0) {
        $html += @"
    <!-- EXTERNAL LIBRARIES -->
$(($CategorizedScripts['External']) -join "`n")

"@
    }

    if ($CategorizedScripts['Firebase'].Count -gt 0) {
        $html += @"
    <!-- FIREBASE & AUTH -->
$(($CategorizedScripts['Firebase']) -join "`n")

"@
    }

    if ($CategorizedScripts['Core'].Count -gt 0) {
        $html += @"
    <!-- CORE APPLICATION -->
$(($CategorizedScripts['Core']) -join "`n")

"@
    }

    if ($CategorizedScripts['UI'].Count -gt 0) {
        $html += @"
    <!-- UI COMPONENTS -->
$(($CategorizedScripts['UI']) -join "`n")

"@
    }

    if ($CategorizedScripts['Utilities'].Count -gt 0) {
        $html += @"
    <!-- UTILITIES -->
$(($CategorizedScripts['Utilities']) -join "`n")

"@
    }

    if ($CategorizedScripts['Inline'].Count -gt 0) {
        $html += @"
    <!-- INLINE SCRIPTS -->
$(($CategorizedScripts['Inline']) -join "`n")

"@
    }

    $html += @"
</body>
</html>
"@

    return $html
}

function Process-HTMLFile {
    param([string]$FilePath)

    Write-Info "Processing: $FilePath"

    if (-not (Test-Path $FilePath)) {
        Write-Warning "File not found: $FilePath"
        return $false
    }

    try {
        # Read the original file
        $originalContent = Get-Content $FilePath -Raw -Encoding UTF8

        if ([string]::IsNullOrWhiteSpace($originalContent)) {
            Write-Warning "File is empty: $FilePath"
            return $false
        }

        # Create backup if requested
        if ($Backup -and -not $WhatIf) {
            $backupPath = Join-Path $BackupDir (Split-Path $FilePath -Leaf)
            Copy-Item $FilePath $backupPath -Force
            Write-Info "  Backup created: $backupPath"
        }

        # Extract components
        $title = Get-PageTitle $originalContent
        $cssLinks = Extract-CSSLinks $originalContent
        $scripts = Extract-Scripts $originalContent
        $bodyContent = Extract-BodyContent $originalContent

        # Categorize components
        $categorizedCSS = Categorize-CSSLinks $cssLinks
        $categorizedScripts = Categorize-Scripts $scripts

        # Build restructured HTML
        $restructuredHTML = Build-RestructuredHTML -Title $title -CategorizedCSS $categorizedCSS -BodyContent $bodyContent -CategorizedScripts $categorizedScripts

        if ($WhatIf) {
            Write-Info "  [WHAT-IF] Would restructure $FilePath"
            Write-Info "  [WHAT-IF] Title: $title"
            Write-Info "  [WHAT-IF] CSS Links: $($cssLinks.Count)"
            Write-Info "  [WHAT-IF] Scripts: $($scripts.Count)"
        } else {
            # Write the restructured file
            $restructuredHTML | Out-File -FilePath $FilePath -Encoding UTF8 -NoNewline
            Write-Success "  ✓ Successfully restructured $FilePath"
        }

        return $true

    } catch {
        Write-Error "  ✗ Error processing $FilePath : $($_.Exception.Message)"
        return $false
    }
}

# Main execution
Write-ColorOutput "============================================" "Magenta"
Write-ColorOutput "  HTML RESTRUCTURING SCRIPT FOR GPACE" "Magenta"
Write-ColorOutput "============================================" "Magenta"
Write-Info ""

if ($WhatIf) {
    Write-Warning "WHAT-IF MODE: No files will be modified"
    Write-Info ""
}

$successCount = 0
$totalCount = 0

foreach ($file in $htmlFiles) {
    $totalCount++

    if (Process-HTMLFile $file) {
        $successCount++
    }

    Write-Info ""
}

# Summary
Write-ColorOutput "============================================" "Magenta"
Write-ColorOutput "  RESTRUCTURING COMPLETE" "Magenta"
Write-ColorOutput "============================================" "Magenta"

if ($WhatIf) {
    Write-Info "WHAT-IF MODE: $totalCount files would be processed"
} else {
    Write-Success "Successfully processed: $successCount/$totalCount files"

    if ($Backup) {
        Write-Info "Backups saved to: $BackupDir"
    }
}

Write-Info ""
Write-Info "Script completed at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
