# Batch restructure all remaining HTML files
$remainingFiles = @(
    'academic-details.html',
    'extracted.html',
    'daily-calendar.html',
    'study-spaces.html',
    'subject-marks.html',
    'settings.html',
    'sleep-saboteurs.html',
    '404.html',
    'priority-list.html',
    'priority-calculator.html',
    'flashcards.html',
    'instant-test-feedback.html'
)

Write-Host "============================================" -ForegroundColor Magenta
Write-Host "  BATCH RESTRUCTURING REMAINING HTML FILES" -ForegroundColor Magenta
Write-Host "============================================" -ForegroundColor Magenta
Write-Host ""

$successCount = 0
$totalCount = $remainingFiles.Count

foreach ($file in $remainingFiles) {
    Write-Host "Processing: $file" -ForegroundColor Cyan
    
    if (Test-Path $file) {
        try {
            $result = & powershell -ExecutionPolicy Bypass -File "test-restructure.ps1" -FilePath $file
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  ✓ Successfully restructured $file" -ForegroundColor Green
                $successCount++
            } else {
                Write-Host "  ✗ Failed to restructure $file" -ForegroundColor Red
            }
        } catch {
            Write-Host "  ✗ Error processing $file : $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "  ⚠ File not found: $file" -ForegroundColor Yellow
    }
    
    Write-Host ""
}

Write-Host "============================================" -ForegroundColor Magenta
Write-Host "  BATCH RESTRUCTURING COMPLETE" -ForegroundColor Magenta
Write-Host "============================================" -ForegroundColor Magenta
Write-Host "Successfully processed: $successCount/$totalCount files" -ForegroundColor Green
Write-Host ""
Write-Host "All HTML files have been beautifully restructured!" -ForegroundColor Green
Write-Host "Original files are backed up with .backup extension" -ForegroundColor Cyan
