@echo off
echo ============================================
echo   HTML RESTRUCTURING PREVIEW (WHAT-IF MODE)
echo ============================================
echo.

echo This will show you what changes would be made to your HTML files
echo WITHOUT actually modifying them. This is safe to run.
echo.

echo Running preview mode...
echo.

powershell -ExecutionPolicy Bypass -File "restructure-html.ps1" -WhatIf

echo.
echo Preview completed!
echo.
echo The output above shows what changes would be made.
echo No files were actually modified.
echo.
echo To apply these changes, run 'run-html-restructure.bat'
echo.
pause
